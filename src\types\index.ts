import type { EmailProvider } from "next-auth/providers/email"

export interface UserProfile {
  id: string;
  name?: string | null;
  email: string;
  image?: string | null;
  originalImage?: string | null;
  isProfileComplete: boolean;
  googleId?: string;
  googleEmail?: string;
  hasGoogleLinked?: boolean;
  isWelcomeEmailSent: boolean;
  isAdmin?: boolean;
  hasAiAccess?: boolean; // Flag to control access to AI features
  createdOn?: string | Date; // Timestamp when the user was created
  lastUpdatedOn?: string | Date; // Timestamp when the user was last updated
  organizationId?: string; // Reference to the organization the user belongs to
  passwordHash?: string; // Hashed password for email/password authentication
  resetPasswordToken?: string; // Token for password reset
  resetPasswordExpires?: number; // Expiration timestamp for password reset token
}

export type InvitePageSize = 'A4' | 'A5' | 'A6' | 'photo4x6' | 'photo5x7' | 'photo6x8' | 'photo8x10';
export type Orientation = 'portrait' | 'landscape';

export interface PrintSettings {
  inviteSettings?: {
    pageSize: InvitePageSize;
    orientation: Orientation;
    labelPosition: {
      x: number; // percentage value
      y: number; // percentage value
    };
    labelScale: number; // percentage value
  };
  labelSettings?: {
    bgColor: string; // Background color for QR label
    qrColor: string; // QR code color
    orientation: Orientation; // Label orientation
  };
}

export interface EventManager {
  userId?: string;
  name?: string;
  email: string;
  image?: string | null;
  role: 'owner' | 'manager' | 'invited';
}

export interface Event {
  ID?: string;
  eventName: string;
  eventDate: Date;
  start: string;
  end: string;
  location: string;
  timezone?: string; // Timezone for the event (e.g., 'Australia/Melbourne')
  rsvpDueDate?: Date; // Due date and time for RSVPs, defaults to 11:59 PM the day before event date

  message: string;

  host: string;
  ownerAccountId: string;  // Account ID of the owner (previously ownerEmail)
  ownerEmail?: string;     // Keep for backward compatibility, will be deprecated
  managers: string[]; // Array of user IDs for managers
  plan?: 'free' | 'host_plus' | 'host_pro';  // Event plan type
  status?: 'pending_payment' | 'active';  // Event status
  paymentStatus?: 'pending' | 'paid' | 'failed';  // Payment status
  paymentId?: string;  // ID of the payment (e.g., Stripe payment intent ID)
  printSettings?: PrintSettings; // Settings for printing QR codes and invites
  organizationId: string; // Reference to the owning organization

  // Partner-specific fields
  createdByPartnerId?: string; // ID of the partner organization that created this event (if applicable)
  venueId?: string; // ID of the venue where the event is taking place (if created by a partner)
  locationId?: string; // ID of the specific location/area within the venue
  maxInvites?: number; // Maximum number of invites allowed for this event
  pricePerAdditionalInvite?: number; // Price per additional invite beyond the maximum
}

export interface EventListItem {
  id: string;
  name: string;
  date: Date | null;
  start?: string;
  end?: string;
  location: string;
  timezone?: string;
  message?: string;
  host: string;
  ownerAccountId: string;  // Account ID of the owner
  ownerEmail?: string;     // Keep for backward compatibility
  guestCount: number;
  plan?: 'free' | 'host_plus' | 'host_pro';  // Event plan type
  status?: 'pending_payment' | 'active';  // Event status
  paymentStatus?: 'pending' | 'paid' | 'failed';  // Payment status
  isManager?: boolean; // Indicates if current user is a manager but not the owner
  raw?: any; // Raw event data
  organizationId?: string; // Reference to the owning organization
}

export interface RSVPReport {
  invites: {
    total: number;
    accepted: number;
    declined: number;
  };
  adults: {
    invited: number;
    accepted: number;
    declined: number;
  };
  children: {
    invited: number;
    accepted: number;
    declined: number;
  };
  total: {
    invited: number;
    accepted: number;
    declined: number;
  };
}

type InviteStatus = "invited" | "accepted" | "declined"

export interface ActivityHistoryItem {
  type: string; // 'invite_link_opened', 'rsvp_response', etc.
  timestamp: string;
  userId?: string | null;
  properties?: Record<string, any>; // For additional data like status, etc.
}

export interface EventInvite {
  ID?: string;
  name: string;
  eventId: string;
  email?: string;
  phone?: string;
  group?: string;
  status: InviteStatus;
  createdAt?: string;
  updatedAt?: string;

  adults: number
  children: number

  message: Message[];

  response?: {
    adults: number;
    children: number;
    timestamp: string;
    message?: string;
  } | null;

  activityHistory?: ActivityHistoryItem[];
}

export interface CSVInvite {
  ID?: string;
  name: string;
  group?: string;
  email: string;
  phone: string;
  adults: number;
  children: number;
}

export interface EventInviteListItem {
  ID: string
  eventId: string
  name: string
  email?: string
  phone?: string
  group?: string
  status: InviteStatus
  adults: number
  children: number
  hasMessage: boolean
  createdAt?: string
  updatedAt?: string
  response?: {
    adults: number
    children: number
    timestamp: string;
    message?: string;
  } | null
}

export interface Message {
  id: string;
  content: string;
  sender: 'guest' | 'host';
  timestamp: Date;
}

export interface EventMedia {

}

export interface Error {
  message: string;
  code: number;
}

export interface Recipient {
  email: string;
  name: string;
}

export interface AuthVerificationRequest {
  identifier: string;
  url: string;
  expires: Date;
  provider: EmailProvider;
  token: string;
  // theme: Theme;
  // request: Request;
}

export interface APIResponse {
  /**
   * HTTP status code
   */
  code: number;
  /**
   * HTTP headers
   */
  headers: Record<string, string>;
  /**
   * Response data
   */
  data: any;
}

export interface PaymentHistoryItem {
  id: string,
  date: string,
  amount: number,
  description: string,
  status: string,
  paymentMethod: string,
  eventId: string,
  userId?: string,
  plan: string,
  receiptUrl?: string | null,
  invoiceUrl?: string | null
}

export interface AnalyticsEvent {
  name: string;
  userId: string | null;
  userType: 'guest' | 'host';
  timestamp: Date;
  properties: Record<string, any>;
}

export interface ImageBuffer {
  fileContents: Buffer<ArrayBufferLike>;
  mimeType: string;
  dimensions: {
    width: number;
    height: number;
  };
};

export interface LabelPrintingOptions {
  orientation: 'portrait' | 'landscape';
  theme: {
    name: string;
    qr: string;
    background: string;
  },
  showBranding: boolean;
}

export interface LabelDimensions {
  dpi: number;
  QR: {
    width: number; // in mm
    height: number; // in mm
    x: number; // percentage value
    y: number; // percentage value
  };
  LANDSCAPE: {
    width: number; // in mm
    height: number; // in mm
  };
  PORTRAIT: {
    width: number; // in mm
    height: number; // in mm
  };
}

export interface Organization {
  id: string;
  name: string;
  type: 'individual' | 'partner';
  members: Array<{
    userId: string;
    role: 'owner' | 'member';
  }>;
  createdOn: string | Date;
  lastUpdatedOn: string | Date;
  contactGroupSettings?: {
    hasBeenAsked: boolean;
  };
}

export interface SavedContactGroup {
  id: string;
  organizationId: string;
  name: string;
  description?: string;
  contacts: Array<{
    email: string;
    name?: string;
    phone?: string;
  }>;
  createdAt: string | Date;
  updatedAt: string | Date;
  createdFromEventId: string;
  isActive?: boolean; // Made optional as we're moving away from soft deletes
}

export interface ContactGroupData {
  name: string;
  contacts: Array<{
    email: string;
    name?: string;
    phone?: string;
  }>;
}

export interface PostEventContactGroupEmailData {
  organizationName: string;
  eventName: string;
  eventDate: string;
  contactGroups: Array<{
    name: string;
    contactCount: number;
  }>;
  saveGroupsUrl: string;
}



// Partner-specific interfaces
export interface Venue {
  id: string;
  name: string;
  address: string;
  description?: string;
  capacity: number;
  organizationId: string; // Reference to the owning partner organization
  locations: VenueLocation[];
  createdOn: string | Date;
  lastUpdatedOn: string | Date;
}

export interface VenueLocation {
  id: string;
  name: string;
  description?: string;
  venueId: string; // Reference to the parent venue
  createdOn: string | Date;
  lastUpdatedOn: string | Date;
}

export interface TimeSlot {
  id: string;
  venueId: string;
  locationId: string;
  date: Date;
  startTime: string;
  endTime: string;
  isBooked: boolean;
  eventId?: string; // Reference to the event if booked
  createdOn: string | Date;
  lastUpdatedOn: string | Date;
}

export interface PartnerInvoice {
  id: string;
  organizationId: string;
  period: {
    start: Date;
    end: Date;
  };
  events: string[]; // Array of event IDs
  amount: number;
  status: 'pending' | 'paid' | 'overdue';
  paymentMethod?: 'card' | 'bank_transfer';
  paymentDate?: Date;
  createdOn: string | Date;
  lastUpdatedOn: string | Date;
}